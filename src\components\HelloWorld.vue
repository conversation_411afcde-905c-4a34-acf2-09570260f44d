<template>
  <div class="container mt-3">
    <div v-html="showHtml"></div>
  </div>
</template>

<script>

export default {
  name: "HelloWorld",

  data() {
    return {
      showHtml: '',
      showAuditData: [],
      flag: '1'
    }
  },

  created() {
    // 获取数据
    this.fetchData();
  },

  methods: {
    fetchData() {
      // 模拟获取数据和模板
      Promise.all([
        fetch('/data.json').then(response => response.json()),
        fetch('/page-template.txt').then(response => response.text())
      ]).then(([data, template]) => {
        this.showAuditData = data;
        this.initData(template, { showAuditData: data, flag: this.flag, changeFlag: this.changeFlag });
      }).catch(error => {
        console.error('获取数据失败:', error);
      });
    },

    changeFlag() {
      this.flag = this.flag === '1' ? '0' : '1';
      // 更新渲染
      this.fetchData();
    },

    initData(description, dataValues) {
      if (!dataValues) {
        alert("该单据未获取到后台数据，请联系开发人员");
        return;
      }
      if (description) {
        try {
          this.showHtml = this.renderTemplate(description, dataValues);
        } catch (error) {
          console.error('渲染模板失败:', error);
        }
      }
    },
    renderTemplate(template, data) {
      return template.replace(/\{\{([\s\S]+?)\}\}/g, (match, key) => {
        try {
          const value = this.getNestedValue(data, key.trim());

          return value !== undefined ? value : '';
        } catch (e) {
          console.warn('替换模板变量失败:', e.message);
          return '';
        }
      });
    },
    getNestedValue(obj, path) {
      const keys = path.replace(/\[(\w+)\]/g, '.$1').split('.');
      let value = obj;
      for (const key of keys) {
        if (value === undefined || value === null) {
          return undefined;
        }
        value = value[key];
      }
      return value;
    }
  }
}
</script>

<style scoped>
.container {
  font-family: Arial, sans-serif;
  max-width: 800px;
  margin: 0 auto;
}
</style>
