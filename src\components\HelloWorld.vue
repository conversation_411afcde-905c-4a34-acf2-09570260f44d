<template>
  <div class="container mt-3">
    <!-- 使用动态组件来渲染包含Vue指令的模板 -->
    <component :is="dynamicComponent" v-if="dynamicComponent"></component>
  </div>
</template>

<script>
import { defineComponent } from 'vue'

export default defineComponent({
  name: "HelloWorld",

  data() {
    return {
      showAuditData: [],
      flag: '1',
      dynamicComponent: null
    }
  },

  created() {
    // 获取数据
    this.fetchData();
  },

  methods: {
    fetchData() {
      // 模拟获取数据和模板
      Promise.all([
        fetch('/data.json').then(response => response.json()),
        fetch('/page-template.txt').then(response => response.text())
      ]).then(([data, template]) => {
        this.showAuditData = data;
        this.createDynamicComponent(template);
      }).catch(error => {
        console.error('获取数据失败:', error);
      });
    },

    changeFlag() {
      this.flag = this.flag === '1' ? '0' : '1';
      console.log('Flag changed to:', this.flag);
      // 重新创建组件以反映状态变化
      this.updateDynamicComponent();
    },

    createDynamicComponent(template) {
      const self = this;

      this.dynamicComponent = defineComponent({
        template: template,
        data() {
          return {
            showAuditData: self.showAuditData,
            flag: self.flag
          }
        },
        methods: {
          changeFlag() {
            self.changeFlag();
          }
        },
        watch: {
          // 监听父组件数据变化
          '$parent.showAuditData': {
            handler(newVal) {
              this.showAuditData = newVal;
            },
            deep: true
          },
          '$parent.flag': {
            handler(newVal) {
              this.flag = newVal;
            }
          }
        }
      });
    },

    updateDynamicComponent() {
      if (this.dynamicComponent) {
        // 强制重新渲染动态组件
        this.$nextTick(() => {
          this.$forceUpdate();
        });
      }
    },

    renderTemplate(template, data) {
      return template.replace(/\{\{([\s\S]+?)\}\}/g, (_, key) => {
        try {
          const value = this.getNestedValue(data, key.trim());
          return value !== undefined ? value : '';
        } catch (e) {
          console.warn('替换模板变量失败:', e.message);
          return '';
        }
      });
    },

    getNestedValue(obj, path) {
      const keys = path.replace(/\[(\w+)\]/g, '.$1').split('.');
      let value = obj;
      for (const key of keys) {
        if (value === undefined || value === null) {
          return undefined;
        }
        value = value[key];
      }
      return value;
    }
  }
})
</script>

<style scoped>
.container {
  font-family: Arial, sans-serif;
  max-width: 800px;
  margin: 0 auto;
}
</style>
