<template>
  <div class="container mt-3">
    <component
      :is="dynamicComponent"
      v-if="dynamicComponent"
      :key="componentKey"
      @update:flag="handleFlagUpdate"
    />
    <div v-else-if="loading" class="loading">
      <div class="spinner"></div>
      <p>正在加载模板...</p>
    </div>
    <div v-else-if="error" class="error">
      <p>加载失败: {{ error }}</p>
      <button @click="fetchData" class="retry-btn">重试</button>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, reactive,  compile } from 'vue'

export default defineComponent({
  name: "AdvancedTemplateRenderer",

  setup() {
    const showAuditData = ref([])
    const flag = ref('1')
    const dynamicComponent = ref(null)
    const componentKey = ref(0)
    const loading = ref(false)
    const error = ref(null)

    // 创建响应式的组件上下文
    const componentContext = reactive({
      showAuditData,
      flag,
      changeFlag: () => {
        flag.value = flag.value === '1' ? '0' : '1'
        console.log('Flag changed to:', flag.value)
        componentKey.value++
      }
    })

    const fetchData = async () => {
      loading.value = true
      error.value = null

      try {
        const [data, template] = await Promise.all([
          fetch('/data.json').then(response => response.json()),
          fetch('/page-template.txt').then(response => response.text())
        ])

        showAuditData.value = data
        await createDynamicComponent(template)
      } catch (err) {
        console.error('获取数据失败:', err)
        error.value = err.message
      } finally {
        loading.value = false
      }
    }

    const createDynamicComponent = async (template) => {
      try {
        // 使用Vue 3的compile函数编译模板
        const render = compile(template)

        dynamicComponent.value = defineComponent({
          setup() {
            return componentContext
          },
          render
        })
      } catch (err) {
        console.error('编译模板失败:', err)
        // 降级到字符串替换方案
        createFallbackComponent(template)
      }
    }

    const createFallbackComponent = (template) => {
      // 处理模板中的Vue指令，转换为安全的HTML
      const processedTemplate = processTemplate(template)

      dynamicComponent.value = defineComponent({
        template: `<div v-html="html"></div>`,
        setup() {
          return {
            html: processedTemplate
          }
        }
      })
    }

    const processTemplate = (template) => {
      // 移除Vue指令，只保留插值表达式
      let processed = template
        .replace(/v-if="[^"]*"/g, '') // 移除v-if
        .replace(/v-for="[^"]*"/g, '') // 移除v-for
        .replace(/v-on:click="[^"]*"/g, '') // 移除点击事件
        .replace(/:key="[^"]*"/g, '') // 移除key绑定
        .replace(/<template[^>]*>/g, '<div>') // 替换template标签
        .replace(/<\/template>/g, '</div>')

      // 替换插值表达式
      processed = processed.replace(/\{\{([\s\S]+?)\}\}/g, (_, key) => {
        try {
          const value = getNestedValue(componentContext, key.trim())
          return value !== undefined ? value : ''
        } catch (e) {
          console.warn('替换模板变量失败:', e.message)
          return ''
        }
      })

      return processed
    }

    const getNestedValue = (obj, path) => {
      const keys = path.replace(/\[(\w+)\]/g, '.$1').split('.')
      let value = obj
      for (const key of keys) {
        if (value === undefined || value === null) {
          return undefined
        }
        value = value[key]
      }
      return value
    }

    const handleFlagUpdate = (newFlag) => {
      flag.value = newFlag
      componentKey.value++
    }

    return {
      showAuditData,
      flag,
      dynamicComponent,
      componentKey,
      loading,
      error,
      fetchData,
      handleFlagUpdate
    }
  },

  created() {
    this.fetchData()
  }
})
</script>

<style scoped>
.container {
  font-family: Arial, sans-serif;
  max-width: 800px;
  margin: 0 auto;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #666;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  text-align: center;
  padding: 20px;
  color: #e74c3c;
  background-color: #fdf2f2;
  border: 1px solid #fecaca;
  border-radius: 4px;
  margin: 20px 0;
}

.retry-btn {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
}

.retry-btn:hover {
  background-color: #2980b9;
}
</style>
