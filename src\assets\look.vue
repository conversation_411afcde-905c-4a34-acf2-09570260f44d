<template>
    <div>
        <div v-html="showHtml"></div>
    </div>
</template>

<script>

export default {
    name: "HelloWorld",

    methods: {
        initData(description, dataValues) {
            if (!dataValues) {
                alert("该单据未获取到后台数据，请联系开发人员");
                return;
            }
            if (description) {
                try {
                    this.showHtml = this.renderTemplate(description, {
                        dataValues: dataValues
                    });
                } catch (error) {
                    console.error('渲染模板失败:', error);
                }
            }
        },
        renderTemplate(template, data) {
            return template.replace(/\{\{([\s\S]+?)\}\}/g, (match, key) => {
                try {
                    const value = this.getNestedValue(data, key.trim());
                    return value !== undefined ? value : '';
                } catch (e) {
                    console.warn('替换模板变量失败:', e.message);
                    return '';
                }
            });
        },
        getNestedValue(obj, path) {
            const keys = path.replace(/\[(\w+)\]/g, '.$1').split('.');
            let value = obj;
            for (const key of keys) {
                if (value === undefined || value === null) {
                    return undefined;
                }
                value = value[key];
            }
            return value;
        }
    }
}
</script>
