<!-- <template>
  <div>
    <div v-if="showAuditData && showAuditData.length > 0 && showAuditData[0].contract">
      <div>
        <div class="font-weight-bolder">{{showAuditData[0].contract.supplierName}}</div>
        <div>{{showAuditData[0].contract.customerName}}</div>
        <div class="d-flex justify-content-between">
          <div>合同编号：{{showAuditData[0].contract.no}}</div>
          <div>原客户订单号：{{showAuditData[0].contract.customerPo}}</div>
        </div>
        <div class="d-flex justify-content-between">
          <div>下单时间：{{showAuditData[0].contract.orderDate}}</div>
          <div class="text-primary font-weight-bolder">业务员：{{showAuditData[0].contract.userName}}</div>
        </div>
        <div style="word-break: break-all;">
          <div>送货地址：{{showAuditData[0].contract.deliveryPlace}}</div>
        </div>
        <div class="d-flex">
          客户需求：
          <div>{{showAuditData[0].contract.freightWay}}</div>
          <div class="pl-2">{{showAuditData[0].contract.deliveryWay}}</div>
          <div class="pl-2">{{showAuditData[0].contract.taxDescript}}</div>
          <div class="pl-2">{{showAuditData[0].contract.payWay}}</div>
        </div>
        <div class="text-primary font-weight-bolder">总金额：{{showAuditData[0].contract.currencyType}}{{showAuditData[0].contract.totalAmt}}</div>
        <div>总面积：{{showAuditData[0].contract.area}}</div>
      </div>
      <div>
        <div class="d-flex justify-content-between align-items-center pb-1 border-bottom">
          <div class="font-weight-bolder">订单明细</div>
          <button class="btn btn-sm btn-primary px-2 py-1" v-on:click="changeFlag">{{ flag && flag == '1' ? '收起' : '展开' }}</button>
        </div>
        <div v-if="flag == '1'">
          <template v-for="item in showAuditData">
            <div class="pt-1 pb-1 border-bottom alert-secondary" :key="item.recordId">
              <div style="word-break: break-all">客户型号：{{item.customerModel}}</div>
              <div>
                <span>数量：{{item.quantity}}</span>
                <span class="pl-2">面积：{{item.deailArea}}</span>
                <span class="pl-2">单价：{{item.price}}</span>
                <span class="pl-2">金额：{{item.totalAmt}}</span>
                <span class="pl-2">业务费：{{item.saleFee}}</span>
                <span class="pl-2">管理费：{{item.manageFee}}</span>
              </div>
              <div class="d-flex justify-content-between">
                <div>PCS：{{item.unitLength}} * {{item.unitWidth}}</div>
                <div>PNL：{{item.pnlLength}} * {{item.pnlWidth}} / {{item.pnlDivisor}}</div>
              </div>
              <div style="word-break: break-all;">
                <span v-if="item.boardLevel">{{item.boardLevel}}</span>
                <span v-if="item.materialType" class="pl-2">{{item.materialType}}</span>
                <span v-if="item.boardThickness" class="pl-2">{{item.boardThickness}}</span>
                <span v-if="item.copperCladThickness" class="pl-2">{{item.copperCladThickness}}</span>
                <span v-if="item.surfaceProcess" class="pl-2">{{item.surfaceProcess}}</span>
                <span v-if="item.solderMaskType" class="pl-2">{{item.solderMaskType}}</span>
                <span v-if="item.characterType" class="pl-2">{{item.characterType}}</span>
                <span v-if="item.shapingWay" class="pl-2">{{item.shapingWay}}</span>
                <span v-if="item.testMethod" class="pl-2">{{item.testMethod}}</span>
                <span v-if="item.smallAperture" class="pl-2">{{item.smallAperture}}</span>
                <span v-if="item.buryBlindHole" class="pl-2">{{item.buryBlindHole}}</span>
                <span v-if="item.deliveryUrgent" class="pl-2">{{item.deliveryUrgent}}</span>
                <span v-if="item.daore" class="pl-2">{{item.daore}}</span>
                <span v-if="item.naiya" class="pl-2">{{item.naiya}}</span>
                <span v-if="item.lingeSpacing" class="pl-2">{{item.lingeSpacing}}</span>
                <span v-if="item.halAhole" class="pl-2">{{item.halAhole}}</span>
                <span v-if="item.resistance" class="pl-2">{{item.resistance}}</span>
                <span v-if="item.pliesnumber" class="pl-2">{{item.pliesnumber}}</span>
                <span v-if="item.specialCraftVal" class="pl-2">{{item.specialCraftVal}}</span>
                <span v-if="item.throughHole" class="pl-2">通孔：{{item.throughHole}}</span>
                <span v-if="item.countersinkHole" class="pl-2">沉头孔：{{item.countersinkHole}}</span>
              </div>
              <div class="d-flex justify-content-between">
                <div>{{item.referenceType}}</div>
                <div class="text-danger font-weight-bolder">{{item.deliveryDate}}</div>
              </div>
              <div>备注：{{item.remark}}</div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "customerPo",
  props: {
    showAuditData: [Object, Array],
  },
  data() {
    return{
      flag: "2"
    }
  },
  methods: {
    changeFlag() {
      if (this.flag == "1"){
        this.flag = "2";
      }else {
        this.flag = "1";
      }
    }
  }
}
</script> -->