<template>
  <div id="app">
    <h1>动态模板渲染解决方案</h1>

    <div class="tabs">
      <button v-for="tab in tabs" :key="tab.key" @click="activeTab = tab.key"
        :class="['tab-btn', { active: activeTab === tab.key }]">
        {{ tab.label }}
      </button>
    </div>

    <div class="tab-content">
      <HelloWorld v-if="activeTab === 'original'" />
      <DynamicTemplateRenderer v-if="activeTab === 'dynamic'" />
      <AdvancedTemplateRenderer v-if="activeTab === 'advanced'" />
    </div>

    <div class="description">
      <h3>{{ currentTabDescription.title }}</h3>
      <p>{{ currentTabDescription.description }}</p>
    </div>
  </div>
</template>

<script>
import HelloWorld from './components/HelloWorld.vue'
import DynamicTemplateRenderer from './components/DynamicTemplateRenderer.vue'
import AdvancedTemplateRenderer from './components/AdvancedTemplateRenderer.vue'

export default {
  name: 'App',
  components: {
    HelloWorld,
    DynamicTemplateRenderer,
    AdvancedTemplateRenderer
  },
  data() {
    return {
      activeTab: 'advanced',
      tabs: [
        { key: 'original', label: '原始方案' },
        { key: 'dynamic', label: '动态组件方案' },
        { key: 'advanced', label: '高级方案（推荐）' }
      ],
      descriptions: {
        original: {
          title: '原始方案 - v-html渲染',
          description: '使用v-html直接渲染HTML，但Vue指令不会被解析执行。'
        },
        dynamic: {
          title: '动态组件方案',
          description: '使用defineComponent动态创建组件，支持Vue指令解析。'
        },
        advanced: {
          title: '高级方案（推荐）',
          description: '使用Vue 3的Composition API和compile函数，提供完整的模板编译支持和错误处理。'
        }
      }
    }
  },
  computed: {
    currentTabDescription() {
      return this.descriptions[this.activeTab] || {}
    }
  }
}
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

h1 {
  font-size: 28px;
  margin-bottom: 30px;
  text-align: center;
  color: #2c3e50;
}

.tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
  border-bottom: 2px solid #e9ecef;
}

.tab-btn {
  background: none;
  border: none;
  padding: 12px 24px;
  cursor: pointer;
  font-size: 16px;
  color: #6c757d;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
  margin: 0 5px;
}

.tab-btn:hover {
  color: #495057;
  background-color: #f8f9fa;
}

.tab-btn.active {
  color: #007bff;
  border-bottom-color: #007bff;
  font-weight: 600;
}

.tab-content {
  min-height: 400px;
  margin-bottom: 30px;
}

.description {
  background-color: #f8f9fa;
  border-left: 4px solid #007bff;
  padding: 20px;
  margin-top: 30px;
  border-radius: 0 4px 4px 0;
}

.description h3 {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 18px;
}

.description p {
  margin: 0;
  color: #6c757d;
  line-height: 1.6;
}
</style>
