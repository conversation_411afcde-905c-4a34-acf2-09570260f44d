<template>
  <div class="container mt-3">
    <!-- 使用动态组件来渲染包含Vue指令的模板 -->
    <component :is="dynamicComponent" v-if="dynamicComponent" :key="componentKey"></component>
    <div v-else class="loading">加载中...</div>
  </div>
</template>

<script>
import { defineComponent } from 'vue'

export default defineComponent({
  name: "DynamicTemplateRenderer",

  data() {
    return {
      showAuditData: [],
      flag: '1',
      dynamicComponent: null,
      componentKey: 0 // 用于强制重新渲染组件
    }
  },

  created() {
    this.fetchData();
  },

  methods: {
    fetchData() {
      Promise.all([
        fetch('/data.json').then(response => response.json()),
        fetch('/page-template.txt').then(response => response.text())
      ]).then(([data, template]) => {
        this.showAuditData = data;
        this.createDynamicComponent(template);
      }).catch(error => {
        console.error('获取数据失败:', error);
      });
    },

    changeFlag() {
      this.flag = this.flag === '1' ? '0' : '1';
      console.log('Flag changed to:', this.flag);
      // 更新组件key来强制重新渲染
      this.componentKey++;
    },

    createDynamicComponent(template) {
      const self = this;
      
      try {
        this.dynamicComponent = defineComponent({
          template: template,
          data() {
            return {
              showAuditData: self.showAuditData,
              flag: self.flag
            }
          },
          methods: {
            changeFlag() {
              self.changeFlag();
            }
          }
        });
      } catch (error) {
        console.error('创建动态组件失败:', error);
        // 降级到安全的HTML渲染
        this.createFallbackComponent(template);
      }
    },

    createFallbackComponent(template) {
      const processedHtml = this.renderTemplate(template, {
        showAuditData: this.showAuditData,
        flag: this.flag
      });

      this.dynamicComponent = defineComponent({
        template: `<div v-html="html"></div>`,
        data() {
          return {
            html: processedHtml
          }
        }
      });
    },

    renderTemplate(template, data) {
      // 简单的模板变量替换
      return template.replace(/\{\{([\s\S]+?)\}\}/g, (_, key) => {
        try {
          const value = this.getNestedValue(data, key.trim());
          return value !== undefined ? value : '';
        } catch (e) {
          console.warn('替换模板变量失败:', e.message);
          return '';
        }
      });
    },

    getNestedValue(obj, path) {
      const keys = path.replace(/\[(\w+)\]/g, '.$1').split('.');
      let value = obj;
      for (const key of keys) {
        if (value === undefined || value === null) {
          return undefined;
        }
        value = value[key];
      }
      return value;
    }
  }
})
</script>

<style scoped>
.container {
  font-family: Arial, sans-serif;
  max-width: 800px;
  margin: 0 auto;
}

.loading {
  text-align: center;
  padding: 20px;
  color: #666;
}
</style>
